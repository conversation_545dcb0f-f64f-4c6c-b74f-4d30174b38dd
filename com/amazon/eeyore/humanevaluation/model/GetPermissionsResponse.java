package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="GetPermissionsResponse")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class GetPermissionsResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for GetPermissionsResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of GetPermissionsResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<PermissionObject> permissions;
    /**
     * Sets the value of the field "permissions" to be used for the constructed object.
     * @param permissions
     *   The value of the "permissions" field.
     * @return
     *   This builder.
     */
    public Builder withPermissions(List<PermissionObject> permissions) {
      this.permissions = permissions;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(GetPermissionsResponse instance) {
      instance.setPermissions(this.permissions);
    }

    /**
     * Builds an instance of GetPermissionsResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public GetPermissionsResponse build() {
      GetPermissionsResponse instance = new GetPermissionsResponse();

      populate(instance);

      return instance;
    }
  };

  private List<PermissionObject> permissions;

  @Required()
  public List<PermissionObject> getPermissions() {
    return this.permissions;
  }

  public void setPermissions(List<PermissionObject> permissions) {
    this.permissions = permissions;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.GetPermissionsResponse");

  /**
   * HashCode implementation for GetPermissionsResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getPermissions());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for GetPermissionsResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof GetPermissionsResponse)) {
      return false;
    }

    GetPermissionsResponse that = (GetPermissionsResponse) other;

    return
        Objects.equals(getPermissions(), that.getPermissions());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("GetPermissionsResponse(");

    ret.append("permissions=");
    ret.append(String.valueOf(permissions));
    ret.append(")");

    return ret.toString();
  }

}
