package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="MediaFile")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class MediaFile extends java.lang.Object  {

  /**
   * Statically creates a builder instance for MediaFile.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of MediaFile.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String url;
    /**
     * Sets the value of the field "url" to be used for the constructed object.
     * @param url
     *   The value of the "url" field.
     * @return
     *   This builder.
     */
    public Builder withUrl(String url) {
      this.url = url;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(MediaFile instance) {
      instance.setUrl(this.url);
    }

    /**
     * Builds an instance of MediaFile.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public MediaFile build() {
      MediaFile instance = new MediaFile();

      populate(instance);

      return instance;
    }
  };

  private String url;

  public String getUrl() {
    return this.url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.MediaFile");

  /**
   * HashCode implementation for MediaFile
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getUrl());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for MediaFile
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof MediaFile)) {
      return false;
    }

    MediaFile that = (MediaFile) other;

    return
        Objects.equals(getUrl(), that.getUrl());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("MediaFile(");

    ret.append("url=");
    ret.append(String.valueOf(url));
    ret.append(")");

    return ret.toString();
  }

}
