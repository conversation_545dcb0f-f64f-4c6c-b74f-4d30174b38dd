package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="ScatterPlotStartPlotRequest")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ScatterPlotStartPlotRequest extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ScatterPlotStartPlotRequest.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ScatterPlotStartPlotRequest.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<ScatterPlotCreativeRender> renders;
    /**
     * Sets the value of the field "renders" to be used for the constructed object.
     * @param renders
     *   The value of the "renders" field.
     * @return
     *   This builder.
     */
    public Builder withRenders(List<ScatterPlotCreativeRender> renders) {
      this.renders = renders;
      return this;
    }

    protected String tag;
    /**
     * Sets the value of the field "tag" to be used for the constructed object.
     * @param tag
     *   The value of the "tag" field.
     * @return
     *   This builder.
     */
    public Builder withTag(String tag) {
      this.tag = tag;
      return this;
    }

    protected ScatterPlotInputDataFile inputDataFile;
    /**
     * Sets the value of the field "inputDataFile" to be used for the constructed object.
     * @param inputDataFile
     *   The value of the "inputDataFile" field.
     * @return
     *   This builder.
     */
    public Builder withInputDataFile(ScatterPlotInputDataFile inputDataFile) {
      this.inputDataFile = inputDataFile;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ScatterPlotStartPlotRequest instance) {
      instance.setRenders(this.renders);
      instance.setTag(this.tag);
      instance.setInputDataFile(this.inputDataFile);
    }

    /**
     * Builds an instance of ScatterPlotStartPlotRequest.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ScatterPlotStartPlotRequest build() {
      ScatterPlotStartPlotRequest instance = new ScatterPlotStartPlotRequest();

      populate(instance);

      return instance;
    }
  };

  private List<ScatterPlotCreativeRender> renders;
  private String tag;
  private ScatterPlotInputDataFile inputDataFile;

  @Required()
  public List<ScatterPlotCreativeRender> getRenders() {
    return this.renders;
  }

  public void setRenders(List<ScatterPlotCreativeRender> renders) {
    this.renders = renders;
  }

  public String getTag() {
    return this.tag;
  }

  public void setTag(String tag) {
    this.tag = tag;
  }

  public ScatterPlotInputDataFile getInputDataFile() {
    return this.inputDataFile;
  }

  public void setInputDataFile(ScatterPlotInputDataFile inputDataFile) {
    this.inputDataFile = inputDataFile;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ScatterPlotStartPlotRequest");

  /**
   * HashCode implementation for ScatterPlotStartPlotRequest
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getRenders(),
        getTag(),
        getInputDataFile());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ScatterPlotStartPlotRequest
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ScatterPlotStartPlotRequest)) {
      return false;
    }

    ScatterPlotStartPlotRequest that = (ScatterPlotStartPlotRequest) other;

    return
        Objects.equals(getRenders(), that.getRenders())
        && Objects.equals(getTag(), that.getTag())
        && Objects.equals(getInputDataFile(), that.getInputDataFile());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ScatterPlotStartPlotRequest(");

    ret.append("renders=");
    ret.append(String.valueOf(renders));
    ret.append(", ");

    ret.append("tag=");
    ret.append(String.valueOf(tag));
    ret.append(", ");

    ret.append("inputDataFile=");
    ret.append(String.valueOf(inputDataFile));
    ret.append(")");

    return ret.toString();
  }

}
