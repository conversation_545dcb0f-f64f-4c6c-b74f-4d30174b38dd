package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="CreatedSignatureEntry")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class CreatedSignatureEntry extends java.lang.Object  {

  /**
   * Statically creates a builder instance for CreatedSignatureEntry.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of CreatedSignatureEntry.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String domain;
    /**
     * Sets the value of the field "domain" to be used for the constructed object.
     * @param domain
     *   The value of the "domain" field.
     * @return
     *   This builder.
     */
    public Builder withDomain(String domain) {
      this.domain = domain;
      return this;
    }

    protected String signatureId;
    /**
     * Sets the value of the field "signatureId" to be used for the constructed object.
     * @param signatureId
     *   The value of the "signatureId" field.
     * @return
     *   This builder.
     */
    public Builder withSignatureId(String signatureId) {
      this.signatureId = signatureId;
      return this;
    }

    protected String createdAt;
    /**
     * Sets the value of the field "createdAt" to be used for the constructed object.
     * @param createdAt
     *   The value of the "createdAt" field.
     * @return
     *   This builder.
     */
    public Builder withCreatedAt(String createdAt) {
      this.createdAt = createdAt;
      return this;
    }

    protected boolean success;
    /**
     * Sets the value of the field "success" to be used for the constructed object.
     * @param success
     *   The value of the "success" field.
     * @return
     *   This builder.
     */
    public Builder withSuccess(boolean success) {
      this.success = success;
      return this;
    }

    protected String errorMessage;
    /**
     * Sets the value of the field "errorMessage" to be used for the constructed object.
     * @param errorMessage
     *   The value of the "errorMessage" field.
     * @return
     *   This builder.
     */
    public Builder withErrorMessage(String errorMessage) {
      this.errorMessage = errorMessage;
      return this;
    }

    protected ValidationResult validationResult;
    /**
     * Sets the value of the field "validationResult" to be used for the constructed object.
     * @param validationResult
     *   The value of the "validationResult" field.
     * @return
     *   This builder.
     */
    public Builder withValidationResult(ValidationResult validationResult) {
      this.validationResult = validationResult;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(CreatedSignatureEntry instance) {
      instance.setDomain(this.domain);
      instance.setSignatureId(this.signatureId);
      instance.setCreatedAt(this.createdAt);
      instance.setSuccess(this.success);
      instance.setErrorMessage(this.errorMessage);
      instance.setValidationResult(this.validationResult);
    }

    /**
     * Builds an instance of CreatedSignatureEntry.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public CreatedSignatureEntry build() {
      CreatedSignatureEntry instance = new CreatedSignatureEntry();

      populate(instance);

      return instance;
    }
  };

  private String domain;
  private String signatureId;
  private String createdAt;
  private boolean success;
  private String errorMessage;
  private ValidationResult validationResult;

  @Required()
  public String getDomain() {
    return this.domain;
  }

  public void setDomain(String domain) {
    this.domain = domain;
  }

  public String getSignatureId() {
    return this.signatureId;
  }

  public void setSignatureId(String signatureId) {
    this.signatureId = signatureId;
  }

  public String getCreatedAt() {
    return this.createdAt;
  }

  public void setCreatedAt(String createdAt) {
    this.createdAt = createdAt;
  }

  @Required()
  public boolean isSuccess() {
    return this.success;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public String getErrorMessage() {
    return this.errorMessage;
  }

  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  public ValidationResult getValidationResult() {
    return this.validationResult;
  }

  public void setValidationResult(ValidationResult validationResult) {
    this.validationResult = validationResult;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.CreatedSignatureEntry");

  /**
   * HashCode implementation for CreatedSignatureEntry
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getDomain(),
        getSignatureId(),
        getCreatedAt(),
        isSuccess(),
        getErrorMessage(),
        getValidationResult());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for CreatedSignatureEntry
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof CreatedSignatureEntry)) {
      return false;
    }

    CreatedSignatureEntry that = (CreatedSignatureEntry) other;

    return
        Objects.equals(getDomain(), that.getDomain())
        && Objects.equals(getSignatureId(), that.getSignatureId())
        && Objects.equals(getCreatedAt(), that.getCreatedAt())
        && Objects.equals(isSuccess(), that.isSuccess())
        && Objects.equals(getErrorMessage(), that.getErrorMessage())
        && Objects.equals(getValidationResult(), that.getValidationResult());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("CreatedSignatureEntry(");

    ret.append("domain=");
    ret.append(String.valueOf(domain));
    ret.append(", ");

    ret.append("signatureId=");
    ret.append(String.valueOf(signatureId));
    ret.append(", ");

    ret.append("createdAt=");
    ret.append(String.valueOf(createdAt));
    ret.append(", ");

    ret.append("success=");
    ret.append(String.valueOf(success));
    ret.append(", ");

    ret.append("errorMessage=");
    ret.append(String.valueOf(errorMessage));
    ret.append(", ");

    ret.append("validationResult=");
    ret.append(String.valueOf(validationResult));
    ret.append(")");

    return ret.toString();
  }

}
