package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="CreativeId")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class CreativeId extends java.lang.Object  {

  /**
   * Statically creates a builder instance for CreativeId.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of CreativeId.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String creativeIdSpace;
    /**
     * Sets the value of the field "creativeIdSpace" to be used for the constructed object.
     * @param creativeIdSpace
     *   The value of the "creativeIdSpace" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeIdSpace(String creativeIdSpace) {
      this.creativeIdSpace = creativeIdSpace;
      return this;
    }

    protected String creativeId;
    /**
     * Sets the value of the field "creativeId" to be used for the constructed object.
     * @param creativeId
     *   The value of the "creativeId" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeId(String creativeId) {
      this.creativeId = creativeId;
      return this;
    }

    protected String variantId;
    /**
     * Sets the value of the field "variantId" to be used for the constructed object.
     * @param variantId
     *   The value of the "variantId" field.
     * @return
     *   This builder.
     */
    public Builder withVariantId(String variantId) {
      this.variantId = variantId;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(CreativeId instance) {
      instance.setCreativeIdSpace(this.creativeIdSpace);
      instance.setCreativeId(this.creativeId);
      instance.setVariantId(this.variantId);
    }

    /**
     * Builds an instance of CreativeId.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public CreativeId build() {
      CreativeId instance = new CreativeId();

      populate(instance);

      return instance;
    }
  };

  private String creativeIdSpace;
  private String creativeId;
  private String variantId;

  @Required()
  public String getCreativeIdSpace() {
    return this.creativeIdSpace;
  }

  public void setCreativeIdSpace(String creativeIdSpace) {
    this.creativeIdSpace = creativeIdSpace;
  }

  @Required()
  public String getCreativeId() {
    return this.creativeId;
  }

  public void setCreativeId(String creativeId) {
    this.creativeId = creativeId;
  }

  public String getVariantId() {
    return this.variantId;
  }

  public void setVariantId(String variantId) {
    this.variantId = variantId;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.CreativeId");

  /**
   * HashCode implementation for CreativeId
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getCreativeIdSpace(),
        getCreativeId(),
        getVariantId());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for CreativeId
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof CreativeId)) {
      return false;
    }

    CreativeId that = (CreativeId) other;

    return
        Objects.equals(getCreativeIdSpace(), that.getCreativeIdSpace())
        && Objects.equals(getCreativeId(), that.getCreativeId())
        && Objects.equals(getVariantId(), that.getVariantId());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("CreativeId(");

    ret.append("creativeIdSpace=");
    ret.append(String.valueOf(creativeIdSpace));
    ret.append(", ");

    ret.append("creativeId=");
    ret.append(String.valueOf(creativeId));
    ret.append(", ");

    ret.append("variantId=");
    ret.append(String.valueOf(variantId));
    ret.append(")");

    return ret.toString();
  }

}
