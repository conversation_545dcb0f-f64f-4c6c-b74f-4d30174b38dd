package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="Annotations")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class Annotations extends java.lang.Object  {

  /**
   * Statically creates a builder instance for Annotations.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of Annotations.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<MalwareDetectionAnnotation> malwareDetections;
    /**
     * Sets the value of the field "malwareDetections" to be used for the constructed object.
     * @param malwareDetections
     *   The value of the "malwareDetections" field.
     * @return
     *   This builder.
     */
    public Builder withMalwareDetections(List<MalwareDetectionAnnotation> malwareDetections) {
      this.malwareDetections = malwareDetections;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(Annotations instance) {
      instance.setMalwareDetections(this.malwareDetections);
    }

    /**
     * Builds an instance of Annotations.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public Annotations build() {
      Annotations instance = new Annotations();

      populate(instance);

      return instance;
    }
  };

  private List<MalwareDetectionAnnotation> malwareDetections;

  public List<MalwareDetectionAnnotation> getMalwareDetections() {
    return this.malwareDetections;
  }

  public void setMalwareDetections(List<MalwareDetectionAnnotation> malwareDetections) {
    this.malwareDetections = malwareDetections;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.Annotations");

  /**
   * HashCode implementation for Annotations
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getMalwareDetections());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for Annotations
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof Annotations)) {
      return false;
    }

    Annotations that = (Annotations) other;

    return
        Objects.equals(getMalwareDetections(), that.getMalwareDetections());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("Annotations(");

    ret.append("malwareDetections=");
    ret.append(String.valueOf(malwareDetections));
    ret.append(")");

    return ret.toString();
  }

}
