package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="RenderScreenCaptureConfiguration")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderScreenCaptureConfiguration extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderScreenCaptureConfiguration.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderScreenCaptureConfiguration.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected boolean enableScreenCapture;
    /**
     * Sets the value of the field "enableScreenCapture" to be used for the constructed object.
     * @param enableScreenCapture
     *   The value of the "enableScreenCapture" field.
     * @return
     *   This builder.
     */
    public Builder withEnableScreenCapture(boolean enableScreenCapture) {
      this.enableScreenCapture = enableScreenCapture;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderScreenCaptureConfiguration instance) {
      instance.setEnableScreenCapture(this.enableScreenCapture);
    }

    /**
     * Builds an instance of RenderScreenCaptureConfiguration.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderScreenCaptureConfiguration build() {
      RenderScreenCaptureConfiguration instance = new RenderScreenCaptureConfiguration();

      populate(instance);

      return instance;
    }
  };

  private boolean enableScreenCapture;

  public boolean isEnableScreenCapture() {
    return this.enableScreenCapture;
  }

  public void setEnableScreenCapture(boolean enableScreenCapture) {
    this.enableScreenCapture = enableScreenCapture;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderScreenCaptureConfiguration");

  /**
   * HashCode implementation for RenderScreenCaptureConfiguration
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        isEnableScreenCapture());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderScreenCaptureConfiguration
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderScreenCaptureConfiguration)) {
      return false;
    }

    RenderScreenCaptureConfiguration that = (RenderScreenCaptureConfiguration) other;

    return
        Objects.equals(isEnableScreenCapture(), that.isEnableScreenCapture());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderScreenCaptureConfiguration(");

    ret.append("enableScreenCapture=");
    ret.append(String.valueOf(enableScreenCapture));
    ret.append(")");

    return ret.toString();
  }

}
