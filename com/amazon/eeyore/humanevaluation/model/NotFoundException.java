package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.AwsQueryError;
import com.amazon.coral.annotation.AwsSoap11Error;
import com.amazon.coral.annotation.AwsSoap12Error;
import com.amazon.coral.annotation.BsfError;
import com.amazon.coral.annotation.HttpError;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="NotFoundException")
@AwsSoap12Error(code="NotFoundException",httpCode=400,type=com.amazon.coral.annotation.AwsErrorType.Sender)
@HttpError(httpCode=404)
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@AwsQueryError(code="NotFoundException",httpCode=400,type=com.amazon.coral.annotation.AwsQueryErrorType.Sender)
@AwsSoap11Error(code="NotFoundException",httpCode=500,type=com.amazon.coral.annotation.AwsErrorType.Sender)
@BsfError(value=com.amazon.coral.annotation.BsfErrorType.BadArgs)
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class NotFoundException extends ClientFaultException  {

  /**
   * Statically creates a builder instance for NotFoundException.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of NotFoundException.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder extends ClientFaultException.Builder {

    @Override
    public Builder withMessage(String message) {
      super.withMessage(message);
      return this;
    }

    private java.lang.Throwable cause;
    /**
     * Sets the cause of this exception.
     * @param cause
     *   The exception cause.
     * @return
     *   This builder.
     */
    public Builder withCause(java.lang.Throwable cause) {
      this.cause = cause;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(NotFoundException instance) {
      super.populate(instance);

    }

    /**
     * Builds an instance of NotFoundException.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public NotFoundException build() {
      NotFoundException instance = new NotFoundException(message, cause);

      populate(instance);

      return instance;
    }
  };

  private static final long serialVersionUID = -1L;

  public NotFoundException() {
  }

  public NotFoundException(Throwable cause) {
    initCause(cause);
  }

  public NotFoundException(String message) {
    super(message);
  }

  public NotFoundException(String message, Throwable cause) {
    super(message);
    initCause(cause);
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.NotFoundException");

  /**
   * HashCode implementation for NotFoundException
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        super.hashCode(),
        classNameHashCode);
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for NotFoundException
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof NotFoundException)) {
      return false;
    }

    return super.equals(other);
  }

}
