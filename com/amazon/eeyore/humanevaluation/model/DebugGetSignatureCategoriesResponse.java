package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="DebugGetSignatureCategoriesResponse")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class DebugGetSignatureCategoriesResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for DebugGetSignatureCategoriesResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of DebugGetSignatureCategoriesResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<String> signatureCategories;
    /**
     * Sets the value of the field "signatureCategories" to be used for the constructed object.
     * @param signatureCategories
     *   The value of the "signatureCategories" field.
     * @return
     *   This builder.
     */
    public Builder withSignatureCategories(List<String> signatureCategories) {
      this.signatureCategories = signatureCategories;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(DebugGetSignatureCategoriesResponse instance) {
      instance.setSignatureCategories(this.signatureCategories);
    }

    /**
     * Builds an instance of DebugGetSignatureCategoriesResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public DebugGetSignatureCategoriesResponse build() {
      DebugGetSignatureCategoriesResponse instance = new DebugGetSignatureCategoriesResponse();

      populate(instance);

      return instance;
    }
  };

  private List<String> signatureCategories;

  @Required()
  @ListMemberConstraint(@NestedConstraints())
  public List<String> getSignatureCategories() {
    return this.signatureCategories;
  }

  public void setSignatureCategories(List<String> signatureCategories) {
    this.signatureCategories = signatureCategories;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.DebugGetSignatureCategoriesResponse");

  /**
   * HashCode implementation for DebugGetSignatureCategoriesResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getSignatureCategories());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for DebugGetSignatureCategoriesResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof DebugGetSignatureCategoriesResponse)) {
      return false;
    }

    DebugGetSignatureCategoriesResponse that = (DebugGetSignatureCategoriesResponse) other;

    return
        Objects.equals(getSignatureCategories(), that.getSignatureCategories());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("DebugGetSignatureCategoriesResponse(");

    ret.append("signatureCategories=");
    ret.append(String.valueOf(signatureCategories));
    ret.append(")");

    return ret.toString();
  }

}
