package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="GetRendersByCreativeIdResponse")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class GetRendersByCreativeIdResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for GetRendersByCreativeIdResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of GetRendersByCreativeIdResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String sourceSystem;
    /**
     * Sets the value of the field "sourceSystem" to be used for the constructed object.
     * @param sourceSystem
     *   The value of the "sourceSystem" field.
     * @return
     *   This builder.
     */
    public Builder withSourceSystem(String sourceSystem) {
      this.sourceSystem = sourceSystem;
      return this;
    }

    protected String sourceProgram;
    /**
     * Sets the value of the field "sourceProgram" to be used for the constructed object.
     * @param sourceProgram
     *   The value of the "sourceProgram" field.
     * @return
     *   This builder.
     */
    public Builder withSourceProgram(String sourceProgram) {
      this.sourceProgram = sourceProgram;
      return this;
    }

    protected List<CreativeId> creativeIds;
    /**
     * Sets the value of the field "creativeIds" to be used for the constructed object.
     * @param creativeIds
     *   The value of the "creativeIds" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeIds(List<CreativeId> creativeIds) {
      this.creativeIds = creativeIds;
      return this;
    }

    protected List<RenderSummary> renderSummaries;
    /**
     * Sets the value of the field "renderSummaries" to be used for the constructed object.
     * @param renderSummaries
     *   The value of the "renderSummaries" field.
     * @return
     *   This builder.
     */
    public Builder withRenderSummaries(List<RenderSummary> renderSummaries) {
      this.renderSummaries = renderSummaries;
      return this;
    }

    protected String continuationToken;
    /**
     * Sets the value of the field "continuationToken" to be used for the constructed object.
     * @param continuationToken
     *   The value of the "continuationToken" field.
     * @return
     *   This builder.
     */
    public Builder withContinuationToken(String continuationToken) {
      this.continuationToken = continuationToken;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(GetRendersByCreativeIdResponse instance) {
      instance.setSourceSystem(this.sourceSystem);
      instance.setSourceProgram(this.sourceProgram);
      instance.setCreativeIds(this.creativeIds);
      instance.setRenderSummaries(this.renderSummaries);
      instance.setContinuationToken(this.continuationToken);
    }

    /**
     * Builds an instance of GetRendersByCreativeIdResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public GetRendersByCreativeIdResponse build() {
      GetRendersByCreativeIdResponse instance = new GetRendersByCreativeIdResponse();

      populate(instance);

      return instance;
    }
  };

  private String sourceSystem;
  private String sourceProgram;
  private List<CreativeId> creativeIds;
  private List<RenderSummary> renderSummaries;
  private String continuationToken;

  @Required()
  public String getSourceSystem() {
    return this.sourceSystem;
  }

  public void setSourceSystem(String sourceSystem) {
    this.sourceSystem = sourceSystem;
  }

  @Required()
  public String getSourceProgram() {
    return this.sourceProgram;
  }

  public void setSourceProgram(String sourceProgram) {
    this.sourceProgram = sourceProgram;
  }

  @Required()
  public List<CreativeId> getCreativeIds() {
    return this.creativeIds;
  }

  public void setCreativeIds(List<CreativeId> creativeIds) {
    this.creativeIds = creativeIds;
  }

  @Required()
  public List<RenderSummary> getRenderSummaries() {
    return this.renderSummaries;
  }

  public void setRenderSummaries(List<RenderSummary> renderSummaries) {
    this.renderSummaries = renderSummaries;
  }

  public String getContinuationToken() {
    return this.continuationToken;
  }

  public void setContinuationToken(String continuationToken) {
    this.continuationToken = continuationToken;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.GetRendersByCreativeIdResponse");

  /**
   * HashCode implementation for GetRendersByCreativeIdResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getSourceSystem(),
        getSourceProgram(),
        getCreativeIds(),
        getRenderSummaries(),
        getContinuationToken());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for GetRendersByCreativeIdResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof GetRendersByCreativeIdResponse)) {
      return false;
    }

    GetRendersByCreativeIdResponse that = (GetRendersByCreativeIdResponse) other;

    return
        Objects.equals(getSourceSystem(), that.getSourceSystem())
        && Objects.equals(getSourceProgram(), that.getSourceProgram())
        && Objects.equals(getCreativeIds(), that.getCreativeIds())
        && Objects.equals(getRenderSummaries(), that.getRenderSummaries())
        && Objects.equals(getContinuationToken(), that.getContinuationToken());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("GetRendersByCreativeIdResponse(");

    ret.append("sourceSystem=");
    ret.append(String.valueOf(sourceSystem));
    ret.append(", ");

    ret.append("sourceProgram=");
    ret.append(String.valueOf(sourceProgram));
    ret.append(", ");

    ret.append("creativeIds=");
    ret.append(String.valueOf(creativeIds));
    ret.append(", ");

    ret.append("renderSummaries=");
    ret.append(String.valueOf(renderSummaries));
    ret.append(", ");

    ret.append("continuationToken=");
    ret.append(String.valueOf(continuationToken));
    ret.append(")");

    return ret.toString();
  }

}
