package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="Screenshot")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class Screenshot extends java.lang.Object  {

  /**
   * Statically creates a builder instance for Screenshot.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of Screenshot.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String id;
    /**
     * Sets the value of the field "id" to be used for the constructed object.
     * @param id
     *   The value of the "id" field.
     * @return
     *   This builder.
     */
    public Builder withId(String id) {
      this.id = id;
      return this;
    }

    protected String url;
    /**
     * Sets the value of the field "url" to be used for the constructed object.
     * @param url
     *   The value of the "url" field.
     * @return
     *   This builder.
     */
    public Builder withUrl(String url) {
      this.url = url;
      return this;
    }

    protected long timestamp;
    /**
     * Sets the value of the field "timestamp" to be used for the constructed object.
     * @param timestamp
     *   The value of the "timestamp" field.
     * @return
     *   This builder.
     */
    public Builder withTimestamp(long timestamp) {
      this.timestamp = timestamp;
      return this;
    }

    protected String renderPartition;
    /**
     * Sets the value of the field "renderPartition" to be used for the constructed object.
     * @param renderPartition
     *   The value of the "renderPartition" field.
     * @return
     *   This builder.
     */
    public Builder withRenderPartition(String renderPartition) {
      this.renderPartition = renderPartition;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(Screenshot instance) {
      instance.setId(this.id);
      instance.setUrl(this.url);
      instance.setTimestamp(this.timestamp);
      instance.setRenderPartition(this.renderPartition);
    }

    /**
     * Builds an instance of Screenshot.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public Screenshot build() {
      Screenshot instance = new Screenshot();

      populate(instance);

      return instance;
    }
  };

  private String id;
  private String url;
  private long timestamp;
  private String renderPartition;

  @Required()
  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  @Required()
  public String getUrl() {
    return this.url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  @Required()
  public long getTimestamp() {
    return this.timestamp;
  }

  public void setTimestamp(long timestamp) {
    this.timestamp = timestamp;
  }

  @Required()
  @EnumValues(value={"creative","landingPage"})
  public String getRenderPartition() {
    return this.renderPartition;
  }

  public void setRenderPartition(String renderPartition) {
    this.renderPartition = renderPartition;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.Screenshot");

  /**
   * HashCode implementation for Screenshot
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getId(),
        getUrl(),
        getTimestamp(),
        getRenderPartition());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for Screenshot
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof Screenshot)) {
      return false;
    }

    Screenshot that = (Screenshot) other;

    return
        Objects.equals(getId(), that.getId())
        && Objects.equals(getUrl(), that.getUrl())
        && Objects.equals(getTimestamp(), that.getTimestamp())
        && Objects.equals(getRenderPartition(), that.getRenderPartition());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("Screenshot(");

    ret.append("id=");
    ret.append(String.valueOf(id));
    ret.append(", ");

    ret.append("url=");
    ret.append(String.valueOf(url));
    ret.append(", ");

    ret.append("timestamp=");
    ret.append(String.valueOf(timestamp));
    ret.append(", ");

    ret.append("renderPartition=");
    ret.append(String.valueOf(renderPartition));
    ret.append(")");

    return ret.toString();
  }

}
