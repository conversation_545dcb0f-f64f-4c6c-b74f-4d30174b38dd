package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="FailedRenderArtifacts")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class FailedRenderArtifacts extends java.lang.Object  {

  /**
   * Statically creates a builder instance for FailedRenderArtifacts.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of FailedRenderArtifacts.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected RenderArtifacts renderArtifacts;
    /**
     * Sets the value of the field "renderArtifacts" to be used for the constructed object.
     * @param renderArtifacts
     *   The value of the "renderArtifacts" field.
     * @return
     *   This builder.
     */
    public Builder withRenderArtifacts(RenderArtifacts renderArtifacts) {
      this.renderArtifacts = renderArtifacts;
      return this;
    }

    protected FailureDetails failureDetails;
    /**
     * Sets the value of the field "failureDetails" to be used for the constructed object.
     * @param failureDetails
     *   The value of the "failureDetails" field.
     * @return
     *   This builder.
     */
    public Builder withFailureDetails(FailureDetails failureDetails) {
      this.failureDetails = failureDetails;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(FailedRenderArtifacts instance) {
      instance.setRenderArtifacts(this.renderArtifacts);
      instance.setFailureDetails(this.failureDetails);
    }

    /**
     * Builds an instance of FailedRenderArtifacts.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public FailedRenderArtifacts build() {
      FailedRenderArtifacts instance = new FailedRenderArtifacts();

      populate(instance);

      return instance;
    }
  };

  private RenderArtifacts renderArtifacts;
  private FailureDetails failureDetails;

  public RenderArtifacts getRenderArtifacts() {
    return this.renderArtifacts;
  }

  public void setRenderArtifacts(RenderArtifacts renderArtifacts) {
    this.renderArtifacts = renderArtifacts;
  }

  public FailureDetails getFailureDetails() {
    return this.failureDetails;
  }

  public void setFailureDetails(FailureDetails failureDetails) {
    this.failureDetails = failureDetails;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.FailedRenderArtifacts");

  /**
   * HashCode implementation for FailedRenderArtifacts
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getRenderArtifacts(),
        getFailureDetails());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for FailedRenderArtifacts
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof FailedRenderArtifacts)) {
      return false;
    }

    FailedRenderArtifacts that = (FailedRenderArtifacts) other;

    return
        Objects.equals(getRenderArtifacts(), that.getRenderArtifacts())
        && Objects.equals(getFailureDetails(), that.getFailureDetails());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("FailedRenderArtifacts(");

    ret.append("renderArtifacts=");
    ret.append(String.valueOf(renderArtifacts));
    ret.append(", ");

    ret.append("failureDetails=");
    ret.append(String.valueOf(failureDetails));
    ret.append(")");

    return ret.toString();
  }

}
