package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.AwsQueryError;
import com.amazon.coral.annotation.AwsSoap11Error;
import com.amazon.coral.annotation.AwsSoap12Error;
import com.amazon.coral.annotation.BsfError;
import com.amazon.coral.annotation.HttpError;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="ForbiddenException")
@AwsSoap12Error(code="ForbiddenException",httpCode=400,type=com.amazon.coral.annotation.AwsErrorType.Sender)
@HttpError(httpCode=403)
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@AwsQueryError(code="ForbiddenException",httpCode=400,type=com.amazon.coral.annotation.AwsQueryErrorType.Sender)
@AwsSoap11Error(code="ForbiddenException",httpCode=500,type=com.amazon.coral.annotation.AwsErrorType.Sender)
@BsfError(value=com.amazon.coral.annotation.BsfErrorType.BadArgs)
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ForbiddenException extends ClientFaultException  {

  /**
   * Statically creates a builder instance for ForbiddenException.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ForbiddenException.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder extends ClientFaultException.Builder {

    @Override
    public Builder withMessage(String message) {
      super.withMessage(message);
      return this;
    }

    private java.lang.Throwable cause;
    /**
     * Sets the cause of this exception.
     * @param cause
     *   The exception cause.
     * @return
     *   This builder.
     */
    public Builder withCause(java.lang.Throwable cause) {
      this.cause = cause;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ForbiddenException instance) {
      super.populate(instance);

    }

    /**
     * Builds an instance of ForbiddenException.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ForbiddenException build() {
      ForbiddenException instance = new ForbiddenException(message, cause);

      populate(instance);

      return instance;
    }
  };

  private static final long serialVersionUID = -1L;

  public ForbiddenException() {
  }

  public ForbiddenException(Throwable cause) {
    initCause(cause);
  }

  public ForbiddenException(String message) {
    super(message);
  }

  public ForbiddenException(String message, Throwable cause) {
    super(message);
    initCause(cause);
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ForbiddenException");

  /**
   * HashCode implementation for ForbiddenException
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        super.hashCode(),
        classNameHashCode);
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ForbiddenException
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ForbiddenException)) {
      return false;
    }

    return super.equals(other);
  }

}
