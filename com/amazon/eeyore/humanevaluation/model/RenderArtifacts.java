package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="RenderArtifacts")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderArtifacts extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderArtifacts.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderArtifacts.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected DisplayRenderArtifacts displayRenderArtifacts;
    /**
     * Sets the value of the field "displayRenderArtifacts" to be used for the constructed object.
     * @param displayRenderArtifacts
     *   The value of the "displayRenderArtifacts" field.
     * @return
     *   This builder.
     */
    public Builder withDisplayRenderArtifacts(DisplayRenderArtifacts displayRenderArtifacts) {
      this.displayRenderArtifacts = displayRenderArtifacts;
      return this;
    }

    protected PageRenderArtifacts pageRenderArtifacts;
    /**
     * Sets the value of the field "pageRenderArtifacts" to be used for the constructed object.
     * @param pageRenderArtifacts
     *   The value of the "pageRenderArtifacts" field.
     * @return
     *   This builder.
     */
    public Builder withPageRenderArtifacts(PageRenderArtifacts pageRenderArtifacts) {
      this.pageRenderArtifacts = pageRenderArtifacts;
      return this;
    }

    protected VastRenderedRenderArtifacts vastRenderedRenderArtifacts;
    /**
     * Sets the value of the field "vastRenderedRenderArtifacts" to be used for the constructed object.
     * @param vastRenderedRenderArtifacts
     *   The value of the "vastRenderedRenderArtifacts" field.
     * @return
     *   This builder.
     */
    public Builder withVastRenderedRenderArtifacts(VastRenderedRenderArtifacts vastRenderedRenderArtifacts) {
      this.vastRenderedRenderArtifacts = vastRenderedRenderArtifacts;
      return this;
    }

    protected VastParsedRenderArtifacts vastParsedRenderArtifacts;
    /**
     * Sets the value of the field "vastParsedRenderArtifacts" to be used for the constructed object.
     * @param vastParsedRenderArtifacts
     *   The value of the "vastParsedRenderArtifacts" field.
     * @return
     *   This builder.
     */
    public Builder withVastParsedRenderArtifacts(VastParsedRenderArtifacts vastParsedRenderArtifacts) {
      this.vastParsedRenderArtifacts = vastParsedRenderArtifacts;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderArtifacts instance) {
      instance.setDisplayRenderArtifacts(this.displayRenderArtifacts);
      instance.setPageRenderArtifacts(this.pageRenderArtifacts);
      instance.setVastRenderedRenderArtifacts(this.vastRenderedRenderArtifacts);
      instance.setVastParsedRenderArtifacts(this.vastParsedRenderArtifacts);
    }

    /**
     * Builds an instance of RenderArtifacts.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderArtifacts build() {
      RenderArtifacts instance = new RenderArtifacts();

      populate(instance);

      return instance;
    }
  };

  private DisplayRenderArtifacts displayRenderArtifacts;
  private PageRenderArtifacts pageRenderArtifacts;
  private VastRenderedRenderArtifacts vastRenderedRenderArtifacts;
  private VastParsedRenderArtifacts vastParsedRenderArtifacts;

  public DisplayRenderArtifacts getDisplayRenderArtifacts() {
    return this.displayRenderArtifacts;
  }

  public void setDisplayRenderArtifacts(DisplayRenderArtifacts displayRenderArtifacts) {
    this.displayRenderArtifacts = displayRenderArtifacts;
  }

  public PageRenderArtifacts getPageRenderArtifacts() {
    return this.pageRenderArtifacts;
  }

  public void setPageRenderArtifacts(PageRenderArtifacts pageRenderArtifacts) {
    this.pageRenderArtifacts = pageRenderArtifacts;
  }

  public VastRenderedRenderArtifacts getVastRenderedRenderArtifacts() {
    return this.vastRenderedRenderArtifacts;
  }

  public void setVastRenderedRenderArtifacts(VastRenderedRenderArtifacts vastRenderedRenderArtifacts) {
    this.vastRenderedRenderArtifacts = vastRenderedRenderArtifacts;
  }

  public VastParsedRenderArtifacts getVastParsedRenderArtifacts() {
    return this.vastParsedRenderArtifacts;
  }

  public void setVastParsedRenderArtifacts(VastParsedRenderArtifacts vastParsedRenderArtifacts) {
    this.vastParsedRenderArtifacts = vastParsedRenderArtifacts;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderArtifacts");

  /**
   * HashCode implementation for RenderArtifacts
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getDisplayRenderArtifacts(),
        getPageRenderArtifacts(),
        getVastRenderedRenderArtifacts(),
        getVastParsedRenderArtifacts());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderArtifacts
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderArtifacts)) {
      return false;
    }

    RenderArtifacts that = (RenderArtifacts) other;

    return
        Objects.equals(getDisplayRenderArtifacts(), that.getDisplayRenderArtifacts())
        && Objects.equals(getPageRenderArtifacts(), that.getPageRenderArtifacts())
        && Objects.equals(getVastRenderedRenderArtifacts(), that.getVastRenderedRenderArtifacts())
        && Objects.equals(getVastParsedRenderArtifacts(), that.getVastParsedRenderArtifacts());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderArtifacts(");

    ret.append("displayRenderArtifacts=");
    ret.append(String.valueOf(displayRenderArtifacts));
    ret.append(", ");

    ret.append("pageRenderArtifacts=");
    ret.append(String.valueOf(pageRenderArtifacts));
    ret.append(", ");

    ret.append("vastRenderedRenderArtifacts=");
    ret.append(String.valueOf(vastRenderedRenderArtifacts));
    ret.append(", ");

    ret.append("vastParsedRenderArtifacts=");
    ret.append(String.valueOf(vastParsedRenderArtifacts));
    ret.append(")");

    return ret.toString();
  }

}
