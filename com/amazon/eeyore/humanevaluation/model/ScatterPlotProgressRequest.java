package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.HttpLabel;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="ScatterPlotProgressRequest")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ScatterPlotProgressRequest extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ScatterPlotProgressRequest.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ScatterPlotProgressRequest.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String outputLocation;
    /**
     * Sets the value of the field "outputLocation" to be used for the constructed object.
     * @param outputLocation
     *   The value of the "outputLocation" field.
     * @return
     *   This builder.
     */
    public Builder withOutputLocation(String outputLocation) {
      this.outputLocation = outputLocation;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ScatterPlotProgressRequest instance) {
      instance.setOutputLocation(this.outputLocation);
    }

    /**
     * Builds an instance of ScatterPlotProgressRequest.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ScatterPlotProgressRequest build() {
      ScatterPlotProgressRequest instance = new ScatterPlotProgressRequest();

      populate(instance);

      return instance;
    }
  };

  private String outputLocation;

  @Required()
  @HttpLabel(value="outputLocation")
  public String getOutputLocation() {
    return this.outputLocation;
  }

  public void setOutputLocation(String outputLocation) {
    this.outputLocation = outputLocation;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ScatterPlotProgressRequest");

  /**
   * HashCode implementation for ScatterPlotProgressRequest
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getOutputLocation());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ScatterPlotProgressRequest
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ScatterPlotProgressRequest)) {
      return false;
    }

    ScatterPlotProgressRequest that = (ScatterPlotProgressRequest) other;

    return
        Objects.equals(getOutputLocation(), that.getOutputLocation());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ScatterPlotProgressRequest(");

    ret.append("outputLocation=");
    ret.append(String.valueOf(outputLocation));
    ret.append(")");

    return ret.toString();
  }

}
