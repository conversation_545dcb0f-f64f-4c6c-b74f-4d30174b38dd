package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="ScatterPlotInputDataFile")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ScatterPlotInputDataFile extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ScatterPlotInputDataFile.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ScatterPlotInputDataFile.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<String> header;
    /**
     * Sets the value of the field "header" to be used for the constructed object.
     * @param header
     *   The value of the "header" field.
     * @return
     *   This builder.
     */
    public Builder withHeader(List<String> header) {
      this.header = header;
      return this;
    }

    protected List<List<String>> data;
    /**
     * Sets the value of the field "data" to be used for the constructed object.
     * @param data
     *   The value of the "data" field.
     * @return
     *   This builder.
     */
    public Builder withData(List<List<String>> data) {
      this.data = data;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ScatterPlotInputDataFile instance) {
      instance.setHeader(this.header);
      instance.setData(this.data);
    }

    /**
     * Builds an instance of ScatterPlotInputDataFile.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ScatterPlotInputDataFile build() {
      ScatterPlotInputDataFile instance = new ScatterPlotInputDataFile();

      populate(instance);

      return instance;
    }
  };

  private List<String> header;
  private List<List<String>> data;

  @ListMemberConstraint(@NestedConstraints())
  public List<String> getHeader() {
    return this.header;
  }

  public void setHeader(List<String> header) {
    this.header = header;
  }

  @ListMemberConstraint(@NestedConstraints())
  public List<List<String>> getData() {
    return this.data;
  }

  public void setData(List<List<String>> data) {
    this.data = data;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ScatterPlotInputDataFile");

  /**
   * HashCode implementation for ScatterPlotInputDataFile
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getHeader(),
        getData());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ScatterPlotInputDataFile
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ScatterPlotInputDataFile)) {
      return false;
    }

    ScatterPlotInputDataFile that = (ScatterPlotInputDataFile) other;

    return
        Objects.equals(getHeader(), that.getHeader())
        && Objects.equals(getData(), that.getData());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ScatterPlotInputDataFile(");

    ret.append("header=");
    ret.append(String.valueOf(header));
    ret.append(", ");

    ret.append("data=");
    ret.append(String.valueOf(data));
    ret.append(")");

    return ret.toString();
  }

}
