package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="ViewportSize")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ViewportSize extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ViewportSize.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ViewportSize.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected int width;
    /**
     * Sets the value of the field "width" to be used for the constructed object.
     * @param width
     *   The value of the "width" field.
     * @return
     *   This builder.
     */
    public Builder withWidth(int width) {
      this.width = width;
      return this;
    }

    protected int height;
    /**
     * Sets the value of the field "height" to be used for the constructed object.
     * @param height
     *   The value of the "height" field.
     * @return
     *   This builder.
     */
    public Builder withHeight(int height) {
      this.height = height;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ViewportSize instance) {
      instance.setWidth(this.width);
      instance.setHeight(this.height);
    }

    /**
     * Builds an instance of ViewportSize.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ViewportSize build() {
      ViewportSize instance = new ViewportSize();

      populate(instance);

      return instance;
    }
  };

  private int width;
  private int height;

  @Required()
  public int getWidth() {
    return this.width;
  }

  public void setWidth(int width) {
    this.width = width;
  }

  @Required()
  public int getHeight() {
    return this.height;
  }

  public void setHeight(int height) {
    this.height = height;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ViewportSize");

  /**
   * HashCode implementation for ViewportSize
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getWidth(),
        getHeight());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ViewportSize
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ViewportSize)) {
      return false;
    }

    ViewportSize that = (ViewportSize) other;

    return
        Objects.equals(getWidth(), that.getWidth())
        && Objects.equals(getHeight(), that.getHeight());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ViewportSize(");

    ret.append("width=");
    ret.append(String.valueOf(width));
    ret.append(", ");

    ret.append("height=");
    ret.append(String.valueOf(height));
    ret.append(")");

    return ret.toString();
  }

}
