package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="GetIdentityResponse")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class GetIdentityResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for GetIdentityResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of GetIdentityResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String username;
    /**
     * Sets the value of the field "username" to be used for the constructed object.
     * @param username
     *   The value of the "username" field.
     * @return
     *   This builder.
     */
    public Builder withUsername(String username) {
      this.username = username;
      return this;
    }

    protected String icon;
    /**
     * Sets the value of the field "icon" to be used for the constructed object.
     * @param icon
     *   The value of the "icon" field.
     * @return
     *   This builder.
     */
    public Builder withIcon(String icon) {
      this.icon = icon;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(GetIdentityResponse instance) {
      instance.setUsername(this.username);
      instance.setIcon(this.icon);
    }

    /**
     * Builds an instance of GetIdentityResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public GetIdentityResponse build() {
      GetIdentityResponse instance = new GetIdentityResponse();

      populate(instance);

      return instance;
    }
  };

  private String username;
  private String icon;

  @Required()
  public String getUsername() {
    return this.username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  @Required()
  public String getIcon() {
    return this.icon;
  }

  public void setIcon(String icon) {
    this.icon = icon;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.GetIdentityResponse");

  /**
   * HashCode implementation for GetIdentityResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getUsername(),
        getIcon());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for GetIdentityResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof GetIdentityResponse)) {
      return false;
    }

    GetIdentityResponse that = (GetIdentityResponse) other;

    return
        Objects.equals(getUsername(), that.getUsername())
        && Objects.equals(getIcon(), that.getIcon());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("GetIdentityResponse(");

    ret.append("username=");
    ret.append(String.valueOf(username));
    ret.append(", ");

    ret.append("icon=");
    ret.append(String.valueOf(icon));
    ret.append(")");

    return ret.toString();
  }

}
