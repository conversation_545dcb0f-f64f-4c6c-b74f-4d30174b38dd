package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.AwsQueryError;
import com.amazon.coral.annotation.AwsSoap11Error;
import com.amazon.coral.annotation.AwsSoap12Error;
import com.amazon.coral.annotation.BsfError;
import com.amazon.coral.annotation.HttpError;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="ClientFaultException")
@AwsSoap12Error(code="ClientFaultException",httpCode=400,type=com.amazon.coral.annotation.AwsErrorType.Sender)
@HttpError(httpCode=400)
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@AwsQueryError(code="ClientFaultException",httpCode=400,type=com.amazon.coral.annotation.AwsQueryErrorType.Sender)
@AwsSoap11Error(code="ClientFaultException",httpCode=500,type=com.amazon.coral.annotation.AwsErrorType.Sender)
@BsfError(value=com.amazon.coral.annotation.BsfErrorType.BadArgs)
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public abstract class ClientFaultException extends RuntimeException  {

  /**
   * Fluent builder for instances of ClientFaultException.
   */
  @com.amazon.coral.annotation.Generated
  public static abstract class Builder {

    protected String message;
    /**
     * Sets the value of the field "message" to be used for the constructed object.
     * @param message
     *   The value of the "message" field.
     * @return
     *   This builder.
     */
    public Builder withMessage(String message) {
      this.message = message;
      return this;
    }

    private java.lang.Throwable cause;
    /**
     * Sets the cause of this exception.
     * @param cause
     *   The exception cause.
     * @return
     *   This builder.
     */
    public Builder withCause(java.lang.Throwable cause) {
      this.cause = cause;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ClientFaultException instance) {
    }
  };

  private static final long serialVersionUID = -1L;

  public ClientFaultException() {
  }

  public ClientFaultException(Throwable cause) {
    initCause(cause);
  }

  public ClientFaultException(String message) {
    super(message);
  }

  public ClientFaultException(String message, Throwable cause) {
    super(message);
    initCause(cause);
  }

  @Override
  public String getMessage() { 
    return super.getMessage();
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ClientFaultException");

  /**
   * HashCode implementation for ClientFaultException
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getMessage());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ClientFaultException
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ClientFaultException)) {
      return false;
    }

    ClientFaultException that = (ClientFaultException) other;

    return
        Objects.equals(getMessage(), that.getMessage());
  }

}
