package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="SearchDetectionsResponse")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class SearchDetectionsResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for SearchDetectionsResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of SearchDetectionsResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<Detection> items;
    /**
     * Sets the value of the field "items" to be used for the constructed object.
     * @param items
     *   The value of the "items" field.
     * @return
     *   This builder.
     */
    public Builder withItems(List<Detection> items) {
      this.items = items;
      return this;
    }

    protected String nextToken;
    /**
     * Sets the value of the field "nextToken" to be used for the constructed object.
     * @param nextToken
     *   The value of the "nextToken" field.
     * @return
     *   This builder.
     */
    public Builder withNextToken(String nextToken) {
      this.nextToken = nextToken;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(SearchDetectionsResponse instance) {
      instance.setItems(this.items);
      instance.setNextToken(this.nextToken);
    }

    /**
     * Builds an instance of SearchDetectionsResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public SearchDetectionsResponse build() {
      SearchDetectionsResponse instance = new SearchDetectionsResponse();

      populate(instance);

      return instance;
    }
  };

  private List<Detection> items;
  private String nextToken;

  @Required()
  public List<Detection> getItems() {
    return this.items;
  }

  public void setItems(List<Detection> items) {
    this.items = items;
  }

  public String getNextToken() {
    return this.nextToken;
  }

  public void setNextToken(String nextToken) {
    this.nextToken = nextToken;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.SearchDetectionsResponse");

  /**
   * HashCode implementation for SearchDetectionsResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getItems(),
        getNextToken());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for SearchDetectionsResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof SearchDetectionsResponse)) {
      return false;
    }

    SearchDetectionsResponse that = (SearchDetectionsResponse) other;

    return
        Objects.equals(getItems(), that.getItems())
        && Objects.equals(getNextToken(), that.getNextToken());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("SearchDetectionsResponse(");

    ret.append("items=");
    ret.append(String.valueOf(items));
    ret.append(", ");

    ret.append("nextToken=");
    ret.append(String.valueOf(nextToken));
    ret.append(")");

    return ret.toString();
  }

}
