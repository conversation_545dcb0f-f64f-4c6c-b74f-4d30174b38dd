package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="VastParsedRenderArtifacts")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class VastParsedRenderArtifacts extends java.lang.Object  {

  /**
   * Statically creates a builder instance for VastParsedRenderArtifacts.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of VastParsedRenderArtifacts.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected PageRenderArtifacts pageRender;
    /**
     * Sets the value of the field "pageRender" to be used for the constructed object.
     * @param pageRender
     *   The value of the "pageRender" field.
     * @return
     *   This builder.
     */
    public Builder withPageRender(PageRenderArtifacts pageRender) {
      this.pageRender = pageRender;
      return this;
    }

    protected String networkTraceLocation;
    /**
     * Sets the value of the field "networkTraceLocation" to be used for the constructed object.
     * @param networkTraceLocation
     *   The value of the "networkTraceLocation" field.
     * @return
     *   This builder.
     */
    public Builder withNetworkTraceLocation(String networkTraceLocation) {
      this.networkTraceLocation = networkTraceLocation;
      return this;
    }

    protected List<MediaFile> mediaFiles;
    /**
     * Sets the value of the field "mediaFiles" to be used for the constructed object.
     * @param mediaFiles
     *   The value of the "mediaFiles" field.
     * @return
     *   This builder.
     */
    public Builder withMediaFiles(List<MediaFile> mediaFiles) {
      this.mediaFiles = mediaFiles;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(VastParsedRenderArtifacts instance) {
      instance.setPageRender(this.pageRender);
      instance.setNetworkTraceLocation(this.networkTraceLocation);
      instance.setMediaFiles(this.mediaFiles);
    }

    /**
     * Builds an instance of VastParsedRenderArtifacts.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public VastParsedRenderArtifacts build() {
      VastParsedRenderArtifacts instance = new VastParsedRenderArtifacts();

      populate(instance);

      return instance;
    }
  };

  private PageRenderArtifacts pageRender;
  private String networkTraceLocation;
  private List<MediaFile> mediaFiles;

  public PageRenderArtifacts getPageRender() {
    return this.pageRender;
  }

  public void setPageRender(PageRenderArtifacts pageRender) {
    this.pageRender = pageRender;
  }

  public String getNetworkTraceLocation() {
    return this.networkTraceLocation;
  }

  public void setNetworkTraceLocation(String networkTraceLocation) {
    this.networkTraceLocation = networkTraceLocation;
  }

  public List<MediaFile> getMediaFiles() {
    return this.mediaFiles;
  }

  public void setMediaFiles(List<MediaFile> mediaFiles) {
    this.mediaFiles = mediaFiles;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.VastParsedRenderArtifacts");

  /**
   * HashCode implementation for VastParsedRenderArtifacts
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getPageRender(),
        getNetworkTraceLocation(),
        getMediaFiles());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for VastParsedRenderArtifacts
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof VastParsedRenderArtifacts)) {
      return false;
    }

    VastParsedRenderArtifacts that = (VastParsedRenderArtifacts) other;

    return
        Objects.equals(getPageRender(), that.getPageRender())
        && Objects.equals(getNetworkTraceLocation(), that.getNetworkTraceLocation())
        && Objects.equals(getMediaFiles(), that.getMediaFiles());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("VastParsedRenderArtifacts(");

    ret.append("pageRender=");
    ret.append(String.valueOf(pageRender));
    ret.append(", ");

    ret.append("networkTraceLocation=");
    ret.append(String.valueOf(networkTraceLocation));
    ret.append(", ");

    ret.append("mediaFiles=");
    ret.append(String.valueOf(mediaFiles));
    ret.append(")");

    return ret.toString();
  }

}
