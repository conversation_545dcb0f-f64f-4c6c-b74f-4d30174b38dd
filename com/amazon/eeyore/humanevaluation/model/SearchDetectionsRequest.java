package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.HttpLabel;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="SearchDetectionsRequest")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class SearchDetectionsRequest extends java.lang.Object  {

  /**
   * Statically creates a builder instance for SearchDetectionsRequest.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of SearchDetectionsRequest.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String signatureId;
    /**
     * Sets the value of the field "signatureId" to be used for the constructed object.
     * @param signatureId
     *   The value of the "signatureId" field.
     * @return
     *   This builder.
     */
    public Builder withSignatureId(String signatureId) {
      this.signatureId = signatureId;
      return this;
    }

    protected String detectionType;
    /**
     * Sets the value of the field "detectionType" to be used for the constructed object.
     * @param detectionType
     *   The value of the "detectionType" field.
     * @return
     *   This builder.
     */
    public Builder withDetectionType(String detectionType) {
      this.detectionType = detectionType;
      return this;
    }

    protected int maxResults;
    /**
     * Sets the value of the field "maxResults" to be used for the constructed object.
     * @param maxResults
     *   The value of the "maxResults" field.
     * @return
     *   This builder.
     */
    public Builder withMaxResults(int maxResults) {
      this.maxResults = maxResults;
      return this;
    }

    protected String nextToken;
    /**
     * Sets the value of the field "nextToken" to be used for the constructed object.
     * @param nextToken
     *   The value of the "nextToken" field.
     * @return
     *   This builder.
     */
    public Builder withNextToken(String nextToken) {
      this.nextToken = nextToken;
      return this;
    }

    protected String createdAfter;
    /**
     * Sets the value of the field "createdAfter" to be used for the constructed object.
     * @param createdAfter
     *   The value of the "createdAfter" field.
     * @return
     *   This builder.
     */
    public Builder withCreatedAfter(String createdAfter) {
      this.createdAfter = createdAfter;
      return this;
    }

    protected String createdBefore;
    /**
     * Sets the value of the field "createdBefore" to be used for the constructed object.
     * @param createdBefore
     *   The value of the "createdBefore" field.
     * @return
     *   This builder.
     */
    public Builder withCreatedBefore(String createdBefore) {
      this.createdBefore = createdBefore;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(SearchDetectionsRequest instance) {
      instance.setSignatureId(this.signatureId);
      instance.setDetectionType(this.detectionType);
      instance.setMaxResults(this.maxResults);
      instance.setNextToken(this.nextToken);
      instance.setCreatedAfter(this.createdAfter);
      instance.setCreatedBefore(this.createdBefore);
    }

    /**
     * Builds an instance of SearchDetectionsRequest.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public SearchDetectionsRequest build() {
      SearchDetectionsRequest instance = new SearchDetectionsRequest();

      populate(instance);

      return instance;
    }
  };

  private String signatureId;
  private String detectionType;
  private int maxResults;
  private String nextToken;
  private String createdAfter;
  private String createdBefore;

  @HttpLabel(value="signatureId")
  @Required()
  public String getSignatureId() {
    return this.signatureId;
  }

  public void setSignatureId(String signatureId) {
    this.signatureId = signatureId;
  }

  @HttpLabel(value="detectionType")
  @Required()
  @EnumValues(value={"Simulated","Online"})
  public String getDetectionType() {
    return this.detectionType;
  }

  public void setDetectionType(String detectionType) {
    this.detectionType = detectionType;
  }

  @HttpLabel(value="maxResults")
  public int getMaxResults() {
    return this.maxResults;
  }

  public void setMaxResults(int maxResults) {
    this.maxResults = maxResults;
  }

  @HttpLabel(value="nextToken")
  public String getNextToken() {
    return this.nextToken;
  }

  public void setNextToken(String nextToken) {
    this.nextToken = nextToken;
  }

  @HttpLabel(value="createdAfter")
  public String getCreatedAfter() {
    return this.createdAfter;
  }

  public void setCreatedAfter(String createdAfter) {
    this.createdAfter = createdAfter;
  }

  @HttpLabel(value="createdBefore")
  public String getCreatedBefore() {
    return this.createdBefore;
  }

  public void setCreatedBefore(String createdBefore) {
    this.createdBefore = createdBefore;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.SearchDetectionsRequest");

  /**
   * HashCode implementation for SearchDetectionsRequest
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getSignatureId(),
        getDetectionType(),
        getMaxResults(),
        getNextToken(),
        getCreatedAfter(),
        getCreatedBefore());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for SearchDetectionsRequest
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof SearchDetectionsRequest)) {
      return false;
    }

    SearchDetectionsRequest that = (SearchDetectionsRequest) other;

    return
        Objects.equals(getSignatureId(), that.getSignatureId())
        && Objects.equals(getDetectionType(), that.getDetectionType())
        && Objects.equals(getMaxResults(), that.getMaxResults())
        && Objects.equals(getNextToken(), that.getNextToken())
        && Objects.equals(getCreatedAfter(), that.getCreatedAfter())
        && Objects.equals(getCreatedBefore(), that.getCreatedBefore());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("SearchDetectionsRequest(");

    ret.append("signatureId=");
    ret.append(String.valueOf(signatureId));
    ret.append(", ");

    ret.append("detectionType=");
    ret.append(String.valueOf(detectionType));
    ret.append(", ");

    ret.append("maxResults=");
    ret.append(String.valueOf(maxResults));
    ret.append(", ");

    ret.append("nextToken=");
    ret.append(String.valueOf(nextToken));
    ret.append(", ");

    ret.append("createdAfter=");
    ret.append(String.valueOf(createdAfter));
    ret.append(", ");

    ret.append("createdBefore=");
    ret.append(String.valueOf(createdBefore));
    ret.append(")");

    return ret.toString();
  }

}
