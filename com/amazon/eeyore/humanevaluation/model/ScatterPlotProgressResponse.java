package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="ScatterPlotProgressResponse")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ScatterPlotProgressResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ScatterPlotProgressResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ScatterPlotProgressResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected boolean finished;
    /**
     * Sets the value of the field "finished" to be used for the constructed object.
     * @param finished
     *   The value of the "finished" field.
     * @return
     *   This builder.
     */
    public Builder withFinished(boolean finished) {
      this.finished = finished;
      return this;
    }

    protected List<ScatterPlotPlotItem> plot;
    /**
     * Sets the value of the field "plot" to be used for the constructed object.
     * @param plot
     *   The value of the "plot" field.
     * @return
     *   This builder.
     */
    public Builder withPlot(List<ScatterPlotPlotItem> plot) {
      this.plot = plot;
      return this;
    }

    protected boolean landingPagesPending;
    /**
     * Sets the value of the field "landingPagesPending" to be used for the constructed object.
     * @param landingPagesPending
     *   The value of the "landingPagesPending" field.
     * @return
     *   This builder.
     */
    public Builder withLandingPagesPending(boolean landingPagesPending) {
      this.landingPagesPending = landingPagesPending;
      return this;
    }

    protected List<ScatterPlotPlotItem> plotLandingPages;
    /**
     * Sets the value of the field "plotLandingPages" to be used for the constructed object.
     * @param plotLandingPages
     *   The value of the "plotLandingPages" field.
     * @return
     *   This builder.
     */
    public Builder withPlotLandingPages(List<ScatterPlotPlotItem> plotLandingPages) {
      this.plotLandingPages = plotLandingPages;
      return this;
    }

    protected String inputDataFile;
    /**
     * Sets the value of the field "inputDataFile" to be used for the constructed object.
     * @param inputDataFile
     *   The value of the "inputDataFile" field.
     * @return
     *   This builder.
     */
    public Builder withInputDataFile(String inputDataFile) {
      this.inputDataFile = inputDataFile;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ScatterPlotProgressResponse instance) {
      instance.setFinished(this.finished);
      instance.setPlot(this.plot);
      instance.setLandingPagesPending(this.landingPagesPending);
      instance.setPlotLandingPages(this.plotLandingPages);
      instance.setInputDataFile(this.inputDataFile);
    }

    /**
     * Builds an instance of ScatterPlotProgressResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ScatterPlotProgressResponse build() {
      ScatterPlotProgressResponse instance = new ScatterPlotProgressResponse();

      populate(instance);

      return instance;
    }
  };

  private boolean finished;
  private List<ScatterPlotPlotItem> plot;
  private boolean landingPagesPending;
  private List<ScatterPlotPlotItem> plotLandingPages;
  private String inputDataFile;

  @Required()
  public boolean isFinished() {
    return this.finished;
  }

  public void setFinished(boolean finished) {
    this.finished = finished;
  }

  public List<ScatterPlotPlotItem> getPlot() {
    return this.plot;
  }

  public void setPlot(List<ScatterPlotPlotItem> plot) {
    this.plot = plot;
  }

  @Required()
  public boolean isLandingPagesPending() {
    return this.landingPagesPending;
  }

  public void setLandingPagesPending(boolean landingPagesPending) {
    this.landingPagesPending = landingPagesPending;
  }

  public List<ScatterPlotPlotItem> getPlotLandingPages() {
    return this.plotLandingPages;
  }

  public void setPlotLandingPages(List<ScatterPlotPlotItem> plotLandingPages) {
    this.plotLandingPages = plotLandingPages;
  }

  public String getInputDataFile() {
    return this.inputDataFile;
  }

  public void setInputDataFile(String inputDataFile) {
    this.inputDataFile = inputDataFile;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ScatterPlotProgressResponse");

  /**
   * HashCode implementation for ScatterPlotProgressResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        isFinished(),
        getPlot(),
        isLandingPagesPending(),
        getPlotLandingPages(),
        getInputDataFile());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ScatterPlotProgressResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ScatterPlotProgressResponse)) {
      return false;
    }

    ScatterPlotProgressResponse that = (ScatterPlotProgressResponse) other;

    return
        Objects.equals(isFinished(), that.isFinished())
        && Objects.equals(getPlot(), that.getPlot())
        && Objects.equals(isLandingPagesPending(), that.isLandingPagesPending())
        && Objects.equals(getPlotLandingPages(), that.getPlotLandingPages())
        && Objects.equals(getInputDataFile(), that.getInputDataFile());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ScatterPlotProgressResponse(");

    ret.append("finished=");
    ret.append(String.valueOf(finished));
    ret.append(", ");

    ret.append("plot=");
    ret.append(String.valueOf(plot));
    ret.append(", ");

    ret.append("landingPagesPending=");
    ret.append(String.valueOf(landingPagesPending));
    ret.append(", ");

    ret.append("plotLandingPages=");
    ret.append(String.valueOf(plotLandingPages));
    ret.append(", ");

    ret.append("inputDataFile=");
    ret.append(String.valueOf(inputDataFile));
    ret.append(")");

    return ret.toString();
  }

}
