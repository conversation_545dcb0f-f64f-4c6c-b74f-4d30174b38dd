package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.HttpLabel;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="GetRenderDataRequest")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class GetRenderDataRequest extends java.lang.Object  {

  /**
   * Statically creates a builder instance for GetRenderDataRequest.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of GetRenderDataRequest.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String creativeIdSpace;
    /**
     * Sets the value of the field "creativeIdSpace" to be used for the constructed object.
     * @param creativeIdSpace
     *   The value of the "creativeIdSpace" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeIdSpace(String creativeIdSpace) {
      this.creativeIdSpace = creativeIdSpace;
      return this;
    }

    protected String creativeId;
    /**
     * Sets the value of the field "creativeId" to be used for the constructed object.
     * @param creativeId
     *   The value of the "creativeId" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeId(String creativeId) {
      this.creativeId = creativeId;
      return this;
    }

    protected String creativeVariantId;
    /**
     * Sets the value of the field "creativeVariantId" to be used for the constructed object.
     * @param creativeVariantId
     *   The value of the "creativeVariantId" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeVariantId(String creativeVariantId) {
      this.creativeVariantId = creativeVariantId;
      return this;
    }

    protected String renderId;
    /**
     * Sets the value of the field "renderId" to be used for the constructed object.
     * @param renderId
     *   The value of the "renderId" field.
     * @return
     *   This builder.
     */
    public Builder withRenderId(String renderId) {
      this.renderId = renderId;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(GetRenderDataRequest instance) {
      instance.setCreativeIdSpace(this.creativeIdSpace);
      instance.setCreativeId(this.creativeId);
      instance.setCreativeVariantId(this.creativeVariantId);
      instance.setRenderId(this.renderId);
    }

    /**
     * Builds an instance of GetRenderDataRequest.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public GetRenderDataRequest build() {
      GetRenderDataRequest instance = new GetRenderDataRequest();

      populate(instance);

      return instance;
    }
  };

  private String creativeIdSpace;
  private String creativeId;
  private String creativeVariantId;
  private String renderId;

  @HttpLabel(value="creativeIdSpace")
  @Required()
  @EnumValues(value={"eriskayCreativeIDSpace","eriskayRodeoCfidSpace","eriskayAxiomIDSpace","eriskayAaxCridSpace","eriskayCanaryIDSpace","eriskayIntegIDSpace","eriskayLoadTestIDSpace"})
  public String getCreativeIdSpace() {
    return this.creativeIdSpace;
  }

  public void setCreativeIdSpace(String creativeIdSpace) {
    this.creativeIdSpace = creativeIdSpace;
  }

  @HttpLabel(value="creativeId")
  @Required()
  public String getCreativeId() {
    return this.creativeId;
  }

  public void setCreativeId(String creativeId) {
    this.creativeId = creativeId;
  }

  @HttpLabel(value="creativeVariantId")
  public String getCreativeVariantId() {
    return this.creativeVariantId;
  }

  public void setCreativeVariantId(String creativeVariantId) {
    this.creativeVariantId = creativeVariantId;
  }

  @HttpLabel(value="renderId")
  @Required()
  public String getRenderId() {
    return this.renderId;
  }

  public void setRenderId(String renderId) {
    this.renderId = renderId;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.GetRenderDataRequest");

  /**
   * HashCode implementation for GetRenderDataRequest
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getCreativeIdSpace(),
        getCreativeId(),
        getCreativeVariantId(),
        getRenderId());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for GetRenderDataRequest
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof GetRenderDataRequest)) {
      return false;
    }

    GetRenderDataRequest that = (GetRenderDataRequest) other;

    return
        Objects.equals(getCreativeIdSpace(), that.getCreativeIdSpace())
        && Objects.equals(getCreativeId(), that.getCreativeId())
        && Objects.equals(getCreativeVariantId(), that.getCreativeVariantId())
        && Objects.equals(getRenderId(), that.getRenderId());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("GetRenderDataRequest(");

    ret.append("creativeIdSpace=");
    ret.append(String.valueOf(creativeIdSpace));
    ret.append(", ");

    ret.append("creativeId=");
    ret.append(String.valueOf(creativeId));
    ret.append(", ");

    ret.append("creativeVariantId=");
    ret.append(String.valueOf(creativeVariantId));
    ret.append(", ");

    ret.append("renderId=");
    ret.append(String.valueOf(renderId));
    ret.append(")");

    return ret.toString();
  }

}
