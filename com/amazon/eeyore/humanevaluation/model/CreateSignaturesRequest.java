package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="CreateSignaturesRequest")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class CreateSignaturesRequest extends java.lang.Object  {

  /**
   * Statically creates a builder instance for CreateSignaturesRequest.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of CreateSignaturesRequest.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<SignatureEntry> signatures;
    /**
     * Sets the value of the field "signatures" to be used for the constructed object.
     * @param signatures
     *   The value of the "signatures" field.
     * @return
     *   This builder.
     */
    public Builder withSignatures(List<SignatureEntry> signatures) {
      this.signatures = signatures;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(CreateSignaturesRequest instance) {
      instance.setSignatures(this.signatures);
    }

    /**
     * Builds an instance of CreateSignaturesRequest.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public CreateSignaturesRequest build() {
      CreateSignaturesRequest instance = new CreateSignaturesRequest();

      populate(instance);

      return instance;
    }
  };

  private List<SignatureEntry> signatures;

  @Required()
  public List<SignatureEntry> getSignatures() {
    return this.signatures;
  }

  public void setSignatures(List<SignatureEntry> signatures) {
    this.signatures = signatures;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.CreateSignaturesRequest");

  /**
   * HashCode implementation for CreateSignaturesRequest
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getSignatures());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for CreateSignaturesRequest
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof CreateSignaturesRequest)) {
      return false;
    }

    CreateSignaturesRequest that = (CreateSignaturesRequest) other;

    return
        Objects.equals(getSignatures(), that.getSignatures());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("CreateSignaturesRequest(");

    ret.append("signatures=");
    ret.append(String.valueOf(signatures));
    ret.append(")");

    return ret.toString();
  }

}
