package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="CreateSignaturesResponse")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class CreateSignaturesResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for CreateSignaturesResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of CreateSignaturesResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected List<CreatedSignatureEntry> createdSignatures;
    /**
     * Sets the value of the field "createdSignatures" to be used for the constructed object.
     * @param createdSignatures
     *   The value of the "createdSignatures" field.
     * @return
     *   This builder.
     */
    public Builder withCreatedSignatures(List<CreatedSignatureEntry> createdSignatures) {
      this.createdSignatures = createdSignatures;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(CreateSignaturesResponse instance) {
      instance.setCreatedSignatures(this.createdSignatures);
    }

    /**
     * Builds an instance of CreateSignaturesResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public CreateSignaturesResponse build() {
      CreateSignaturesResponse instance = new CreateSignaturesResponse();

      populate(instance);

      return instance;
    }
  };

  private List<CreatedSignatureEntry> createdSignatures;

  @Required()
  public List<CreatedSignatureEntry> getCreatedSignatures() {
    return this.createdSignatures;
  }

  public void setCreatedSignatures(List<CreatedSignatureEntry> createdSignatures) {
    this.createdSignatures = createdSignatures;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.CreateSignaturesResponse");

  /**
   * HashCode implementation for CreateSignaturesResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getCreatedSignatures());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for CreateSignaturesResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof CreateSignaturesResponse)) {
      return false;
    }

    CreateSignaturesResponse that = (CreateSignaturesResponse) other;

    return
        Objects.equals(getCreatedSignatures(), that.getCreatedSignatures());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("CreateSignaturesResponse(");

    ret.append("createdSignatures=");
    ret.append(String.valueOf(createdSignatures));
    ret.append(")");

    return ret.toString();
  }

}
